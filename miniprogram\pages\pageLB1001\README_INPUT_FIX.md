# 微信小程序安卓输入框文字飘出问题修复

## 问题描述
在安卓手机上，微信小程序的输入框在聚焦时可能出现文字飘出输入框的问题。

## 解决方案

### 1. TypeScript 文件修改 (pageLB1001.ts)

#### 新增数据属性
```typescript
data: {
  // ... 其他属性
  paddingBottom: 0,
  // 新增：输入框位置相关数据
  inputTop: 0,
  inputHeight: 0,
  windowHeight: 0,
  pageTransform: '',
}
```

#### 优化的聚焦和失焦处理函数
```typescript
// 处理输入框聚焦事件
handleInputFocus() {
  console.log("聚焦");
  const query = wx.createSelectorQuery();
  query.select(".toast_input").boundingClientRect();
  query.exec((res) => {
    if (res[0]) {
      const inputRect = res[0];
      const inputTop = inputRect.top;
      const inputHeight = inputRect.height;
      
      // 获取系统信息
      const systemInfo = wx.getSystemInfoSync();
      const windowHeight = systemInfo.windowHeight;
      
      this.setData({
        inputTop: inputTop,
        inputHeight: inputHeight,
        windowHeight: windowHeight,
      });
      
      console.log("输入框位置信息", { inputTop, inputHeight, windowHeight });
    }
  });
  
  this.handleShowInput();
},

// 处理输入框失焦事件
handleInputBlur() {
  console.log("失焦");
  // 失焦时重置页面位置
  this.setData({
    paddingBottom: 0,
    pageTransform: "",
  });
},

handleShowInput() {
  // 开始监听键盘高度变化
  wx.onKeyboardHeightChange((res) => {
    console.log("键盘高度变化", res);
    const keyboardHeight = res.height;

    if (keyboardHeight > 0) {
      // 键盘弹起时的处理
      const inputTop = this.data.inputTop || 0;
      const windowHeight = this.data.windowHeight || 0;

      // 计算输入框是否被键盘遮挡
      const inputBottom = inputTop + (this.data.inputHeight || 0);
      const availableHeight = windowHeight - keyboardHeight;

      if (inputBottom > availableHeight) {
        // 输入框被遮挡，需要向上移动页面
        const moveDistance = inputBottom - availableHeight + 100; // 额外留100px空间
        this.setData({
          pageTransform: `translateY(-${moveDistance}px)`,
          paddingBottom: keyboardHeight,
        });
      } else {
        // 输入框未被遮挡，只设置底部padding
        this.setData({
          paddingBottom: keyboardHeight,
          pageTransform: "",
        });
      }
    } else {
      // 键盘收起时重置
      this.setData({
        paddingBottom: 0,
        pageTransform: "",
      });
    }
  });
}
```

### 2. WXML 文件修改 (pageLB1001.wxml)

#### 页面容器添加动态样式
```xml
<view class="pageBox" style="transform: {{pageTransform}}; padding-bottom: {{paddingBottom}}px; transition: transform 0.3s ease;">
```

#### 输入框添加聚焦和失焦事件
```xml
<!-- 修改密码输入框 -->
<input bind:focus="handleInputFocus" bind:blur="handleInputBlur" type="text" maxlength="6" placeholder="请输入密码" bindinput="onInput" />

<!-- 校验密码输入框 -->
<input bind:focus="handleInputFocus" bind:blur="handleInputBlur" type="text" placeholder="请输入密码" maxlength="6" bindinput="onInputow" />

<!-- 忘记密码输入框 -->
<input bind:focus="handleInputFocus" bind:blur="handleInputBlur" type="text" placeholder="请输入新密码" maxlength="6" bindinput="onInputft" />
```

### 3. LESS 样式文件修改 (pageLB1001.less)

#### 优化输入框样式
```less
.toast_input {
  text-align: center;
  font-size: 40rpx;
  color: #0b83ff;
  margin: 45rpx 45rpx;
  border: 1px solid rgb(212, 212, 212);
  padding: 15rpx 15rpx;
  width: 80%;
  box-sizing: border-box;
  overflow: hidden;
}

.toast_input input {
  width: 100%;
  height: 100%;
  text-align: center;
  font-size: 40rpx;
  color: #0b83ff;
  border: none;
  outline: none;
  background: transparent;
  box-sizing: border-box;
  line-height: normal;
  vertical-align: middle;
}
```

#### 页面容器样式优化
```less
.pageBox {
  height: 100vh;
  overflow-y: auto;
  overflow-x: hidden;
  box-sizing: border-box;
  transition: transform 0.3s ease;
}
```

## 修复原理

1. **位置检测**: 通过 `wx.createSelectorQuery()` 获取输入框的准确位置信息
2. **键盘监听**: 使用 `wx.onKeyboardHeightChange()` 监听键盘高度变化
3. **智能调整**: 根据输入框位置和键盘高度计算是否需要移动页面
4. **平滑过渡**: 使用 CSS `transform` 和 `transition` 实现平滑的页面移动效果
5. **样式优化**: 通过 CSS 样式防止文字溢出输入框

## 测试建议

1. 在安卓手机上测试各个输入框的聚焦和失焦
2. 检查键盘弹起时页面是否正确调整
3. 验证键盘收起时页面是否正确恢复
4. 测试不同屏幕尺寸的适配效果

## 注意事项

- 该修复主要针对安卓设备的输入框问题
- iOS 设备通常不会出现此问题，但修复方案对 iOS 也是兼容的
- 如果遇到其他输入框问题，可以参考此方案进行类似修复
