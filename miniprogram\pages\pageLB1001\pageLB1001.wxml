<view class="pageBox">
  <view class="head">
    <view class="return" catchtap="returnSetp">
      <image class="return-img" src="/images/fanhui.png"></image>
    </view>
    <text class="title">雷达感应智能门禁</text>
  </view>
  <view class="font-blue other-btn" catch:tap="toPageSetting">
    固件升级
    <view style="margin-right: auto;text-indent: 26rpx;">版本：V{{Version}}</view>
  </view>


  <view class="header">
    <view class="doorContainer">
      <image src="../../images/LB1001/men-1.png" class="door" />
      <image src="../../images/LB1001/men-left.png" style="{{openDoor_left}}" class="door_left" />
      <image src="../../images/LB1001/men-right.png" style="{{openDoor_right}}" class="door_right" />
      <!-- <image src="../../images/LB1001/men-left.png" class="door_left" bind:tap="connectOpen"/>
      <image src="../../images/LB1001/men-right.png" class="door_right" bind:tap="connectOpen"/> -->
    </view>
    <!--   -->
    <view class="statusText">当前状态：{{openValue}}</view>
  </view>





  <!-- <view class="open_and_close_door" style="margin-top:30rpx;">
    <view bind:tap="openAndClose_door" class="{{openValue=='开门'?'blue':''}}" data-status="1">开门</view>
    <view style="margin-left: auto;" bind:tap="openAndClose_door" class=" {{openValue=='关门'?'blue':''}}"
      data-status="0">关门</view>
  </view> -->

  <view class="item pb" style="margin-top:30rpx;">
    <view class="label" style="position: relative;">
      <view style="text-indent: 40rpx;">雷达开关</view>
      <!-- <view class="wenhao" capture-bind:tap="handleWenHao">?</view> -->
      <!-- 气泡弹窗 -->
      <!-- <view class="mask_bubble" wx:if="{{showBubble}}"  capture-bind:tap="handleWenHao">

        </view> -->
      <!-- <view class="bubbleToast" wx:if="{{showBubble}}" bind:tap="handleWenHao">
        <view>
          开启此功能:"开门" 和 "关门" 按钮仅具备临时开关门功能。关闭此功能，雷达感应功能将关闭。
        </view>
      </view> -->

    </view>
    <view style="margin-left: auto;margin-right: 0rpx;">
      <switch checked="{{checked}}" bindchange="changeChecked" color="#32a4f5" />
    </view>
  </view>
  <view class="tip">(当前雷达已开启，无法手动开关门)</view>


  <!-- 检测无人关门时间 -->
  <view class="item" style="padding:20rpx 0rpx 10rpx 0rpx;">
    <view class="label" style="text-indent: 40rpx;">关门延时<text
        style="color: #7A7A7A;font-size: 28rpx;margin-left: 1rpx;letter-spacing: 0px;">(范围：1-60秒)</text> </view>
    <!-- 内容 -->
    <view class="label2">
      <view style="margin-right: 14rpx;color: #888888;">{{silderValue2}}s</view>
    </view>
  </view>
  <!-- 滑块 -->
  <view class="item"
    style="flex-direction: column;padding-top: 0rpx;margin-top: -20rpx;box-shadow: 2px 4px 4px #ccc;padding:10rpx 0rpx 20rpx 0rpx;border-radius: 0 0 8px 8px;">
    <view>
      <view style="width: 100%;">
        <slider bindchanging="sliderchanging2" bindchange="bindchange2" min="0" max="60" style="width: 91%;"
          activeColor="#49b0fae1" value="{{silderValue2}}" />
      </view>
      <view style="display: flex; width: 95.8%;margin-left: 2%;margin-top: -10rpx;">
        <view style="color: #888888;">1</view>
        <view style="margin-left: auto;color: #888888;">60</view>
      </view>
    </view>
    <view></view>
  </view>
  <!-- 感应范围 -->
  <view class="item">
    <view class="label" style="text-indent: 40rpx;">感应范围<text
        style="color: #7A7A7A;font-size: 28rpx;margin-left: 1rpx;letter-spacing: 0px;">(档位*0.7m)</text> </view>
    <!-- 内容 -->
    <view class="label2">
      <view style="margin-right: 14rpx;color: #888888;">{{silderValue1}}档</view>
    </view>
  </view>
  <!-- 滑块 -->
  <view class="item"
    style="flex-direction: column;padding-top: 0rpx;margin-top: -20rpx;box-shadow: 2px 4px 4px #ccc;border-radius: 0 0 8px 8px;">
    <view>
      <view style="width: 100%;">
        <slider bindchanging="sliderchanging" bindchange="bindchange" min="0" max="15" style="width: 91%;"
          activeColor="#49b0fae1" value="{{silderValue1}}" />
      </view>
      <view style="display: flex; width: 95.8%;margin-left: 2%;margin-top: -10rpx;">
        <view style="color: #888888;">1</view>
        <view style="margin-left: auto;color: #888888;">15</view>
      </view>
    </view>
    <view>
      <!-- 雷达图 -->
      <view class="temp">{{max15cm}}</view>
      <view class="radar">
        <view class="radar-scan"></view>
        <view class="cm">{{distanceCM}}m</view>
      </view>
      <view class="item" style="box-shadow: none;background-color: transparent;margin-top: -40rpx;">
        <button class="btn" bind:tap="Adjustjl">智能调距</button>
      </view>
    </view>
    <view class="item-tip">（根据雷达当前感应到的人设定档位）</view>
  </view>
  <view class="forgetPwd" bind:tap="onForgetPwd" data-type="0">忘记密码 ?</view>
  <view class="update-pwd blue" bind:tap="updatePwd">
    修改密码
  </view>
  <view class="toast" wx:if="{{isShowToast}}" bind:tap="closeToast">
    <view class="mask"></view>
    <view class="toastBox">
      <view class="toast_close"> × </view>
      <view class="toast_icon">
        <image src="{{toastSuccess === true?'../../images/chenggong.png':'../../images/shibai.png'}}"
          mode="aspectFit" />
      </view>
      <view class="toast_text {{toastSuccess === true?'blue':'red'}}">
        {{toastText}}
      </view>
    </view>
  </view>
  <!-- 设置密码弹窗 -->
  <view class="toast" wx:if="{{isShowPwd}}">
    <view class="mask"></view>
    <view class="toastBox-a">
      <view class="toast_head">
        修改密码
      </view>
      <!-- <view class="toast_text-a" wx:if="{{isInput}}">
        是否修改控制密码？
      </view> -->
      <view class="toast_input">
        <input bind:focus="handleInputFocus" type="text" maxlength="6" placeholder="请输入密码"
          bindinput="onInput" />
      </view>
      <view class="toast_but">
        <button class="toast_item-a" style="margin-left: 50rpx;" bind:tap="closePwd">取消</button>
        <button class="toast_item-b" style="margin-left: 50rpx;" bind:tap="editPwd">确定</button>
      </view>
    </view>
  </view>

  <!-- 校验密码弹窗 -->
  <view class="toast" wx:if="{{isValidation}}">
    <view class="mask"></view>
    <view class="toastBox-a">
      <view class="toast_head">
        校验密码
      </view>
      <view class="forgetPwd2" bind:tap="onForgetPwd" data-type="1">忘记密码 ?</view>
      <view class="toast_input">
        <input type="text" placeholder="请输入密码" maxlength="6" bindinput="onInputow" />
      </view>
      <view class="toast_but">
        <button class="toast_item-a" style="margin-left: 50rpx;" bind:tap="closeVld">取消</button>
        <button class="toast_item-b" style="margin-left: 50rpx;" bind:tap="validaPwd">确定</button>
      </view>
    </view>
  </view>

  <!-- 忘记密码弹窗 -->
  <view class="toast" wx:if="{{isForget}}">
    <view class="mask"></view>
    <view class="toastBox-a">
      <view class="toast_head">
        忘记密码
      </view>
      <view class="toast_input">
        <input type="text" placeholder="请输入新密码" maxlength="6" bindinput="onInputft" />
      </view>
      <view class="toast_but">
        <button class="toast_item-a" style="margin-left: 50rpx;" bind:tap="closeForget">取消</button>
        <button class="toast_item-b" style="margin-left: 50rpx;" bind:tap="validaForget">确定</button>
      </view>
    </view>
  </view>



</view>